#!/usr/bin/env python3
"""
Script-Based State Builder for GretahAI ScriptWeaver

This module provides functionality to build browser state by executing partial test scripts
instead of manually replaying individual steps. This approach leverages the proven script
execution infrastructure and provides better reliability than manual step replay.

Key Features:
- Generate partial test scripts containing steps 1 through N
- Execute partial scripts using existing pytest infrastructure
- Maintain browser session state for interactive element selection
- Integrate with existing script generation and execution pipeline
- Provide comprehensive error handling and fallback mechanisms

Benefits over Manual Step Replay:
- Leverages proven script execution reliability
- Utilizes existing pytest infrastructure and error handling
- Better handling of complex multi-step scenarios with dynamic content
- Reduced complexity by reusing established patterns
"""

import os
import sys
import time
import logging
import tempfile
import subprocess
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

# Configure logging
logger = logging.getLogger("ScriptWeaver.script_based_state_builder")

class ScriptBasedStateBuilder:
    """
    Build browser state by executing partial test scripts instead of manual step replay.
    
    This class generates and executes partial test scripts containing only the steps
    needed to reach the target state, then provides the prepared browser session
    for interactive element selection.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the script-based state builder.
        
        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        self.last_error = None
        self.execution_results = {}
        self.partial_script_path = None
        
    def build_state_with_script(self, 
                               step_table: List[Dict[str, Any]], 
                               current_step_index: int,
                               website_url: str,
                               test_data: Dict[str, Any] = None,
                               test_case_id: str = "unknown") -> Tuple[bool, str, Optional[str]]:
        """
        Build browser state by executing a partial test script.
        
        Args:
            step_table: List of test steps in automation-ready format
            current_step_index: Index of the current step (0-based)
            website_url: Base website URL for navigation
            test_data: Test data dictionary for parameter substitution
            test_case_id: Test case identifier for script naming
            
        Returns:
            Tuple of (success: bool, message: str, browser_session_info: Optional[str])
        """
        logger.info(f"=== Starting script-based state building for step {current_step_index + 1} ===")
        
        if not step_table or current_step_index <= 0:
            logger.info("No previous steps to execute")
            return True, "No previous steps to execute", None
            
        try:
            # Generate partial script containing steps 1 through current_step_index-1
            partial_script_content = self._generate_partial_script(
                step_table, current_step_index, website_url, test_data, test_case_id
            )
            
            if not partial_script_content:
                return False, "Failed to generate partial script", None
            
            # Save partial script to temporary file
            script_path = self._save_partial_script(partial_script_content, test_case_id, current_step_index)
            if not script_path:
                return False, "Failed to save partial script", None
            
            self.partial_script_path = script_path
            
            # Execute partial script using pytest infrastructure
            success, message, browser_info = self._execute_partial_script(script_path, test_case_id)
            
            if success:
                logger.info(f"Script-based state building completed successfully")
                return True, message, browser_info
            else:
                logger.error(f"Script-based state building failed: {message}")
                self.last_error = message
                return False, message, None
                
        except Exception as e:
            error_message = f"Unexpected error during script-based state building: {str(e)}"
            logger.error(error_message, exc_info=True)
            self.last_error = error_message
            return False, error_message, None
    
    def _generate_partial_script(self, 
                                step_table: List[Dict[str, Any]], 
                                current_step_index: int,
                                website_url: str,
                                test_data: Dict[str, Any],
                                test_case_id: str) -> Optional[str]:
        """
        Generate a partial test script containing only the steps needed to reach target state.
        
        Args:
            step_table: List of test steps
            current_step_index: Current step index (0-based)
            website_url: Base website URL
            test_data: Test data for parameter substitution
            test_case_id: Test case identifier
            
        Returns:
            Generated script content as string, or None if generation fails
        """
        try:
            logger.info(f"Generating partial script for steps 1-{current_step_index}")
            
            # Extract steps to include (1 through current_step_index-1)
            steps_to_include = step_table[:current_step_index]
            
            # Generate script header
            script_content = self._generate_script_header(test_case_id, current_step_index, steps_to_include)
            
            # Generate imports and setup
            script_content += self._generate_script_imports()
            
            # Generate test data section
            script_content += self._generate_test_data_section(test_data, website_url)
            
            # Generate test functions for each step
            for i, step in enumerate(steps_to_include):
                step_function = self._generate_step_function(step, i + 1, test_data)
                if step_function:
                    script_content += step_function
                else:
                    logger.warning(f"Failed to generate function for step {i + 1}")
            
            # Generate script footer with browser session preservation
            script_content += self._generate_script_footer()
            
            logger.debug(f"Generated partial script with {len(steps_to_include)} steps")
            return script_content
            
        except Exception as e:
            logger.error(f"Error generating partial script: {str(e)}")
            return None
    
    def _generate_script_header(self, test_case_id: str, current_step_index: int, steps: List[Dict[str, Any]]) -> str:
        """Generate script header with metadata."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        return f'''# =========================================================================
# PARTIAL SCRIPT FOR STATE BUILDING - {test_case_id.upper()}
# =========================================================================
# Generated: {timestamp}
# Purpose: Execute steps 1-{current_step_index} to build browser state
# Target Step: {current_step_index + 1}
# Steps Included: {len(steps)}
# 
# This script is automatically generated for script-based state building.
# It executes the necessary steps to reach the correct browser state before
# launching the interactive element selector.
# =========================================================================

'''
    
    def _generate_script_imports(self) -> str:
        """Generate standard imports for the partial script."""
        return '''import pytest
import time
import random
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException

'''
    
    def _generate_test_data_section(self, test_data: Dict[str, Any], website_url: str) -> str:
        """Generate test data section."""
        test_data_str = "{\n"
        
        # Add website URL
        test_data_str += f'    "website_url": "{website_url}",\n'
        
        # Add test data parameters
        if test_data:
            for key, value in test_data.items():
                if isinstance(value, str):
                    test_data_str += f'    "{key}": "{value}",\n'
                else:
                    test_data_str += f'    "{key}": {repr(value)},\n'
        
        test_data_str = test_data_str.rstrip(',\n') + '\n}'
        
        return f'''# Test data for partial script execution
TEST_DATA = {test_data_str}

'''
    
    def _generate_step_function(self, step: Dict[str, Any], step_no: int, test_data: Dict[str, Any]) -> Optional[str]:
        """
        Generate a test function for a single step.
        
        Args:
            step: Step data dictionary
            step_no: Step number (1-based)
            test_data: Test data for parameter substitution
            
        Returns:
            Generated function code as string
        """
        try:
            action = step.get('action', '').lower()
            locator_strategy = step.get('locator_strategy', '')
            locator = step.get('locator', '')
            test_data_param = step.get('test_data_param', '')
            timeout = step.get('timeout', 10)
            step_description = step.get('step_description', f'Execute {action}')
            
            # Substitute test data parameters
            if test_data_param and test_data:
                param_key = test_data_param.strip('{}')
                if param_key in test_data:
                    test_data_param = test_data[param_key]
            
            function_name = f"test_step{step_no}_{action}"
            
            function_code = f'''@pytest.mark.order({step_no})
def {function_name}(browser):
    """
    Step {step_no}: {step_description}
    Action: {action}
    Locator: {locator_strategy}={locator}
    """
    try:
'''
            
            # Generate action-specific code
            if action == 'navigate':
                function_code += self._generate_navigate_code(locator, timeout)
            elif action == 'click':
                function_code += self._generate_click_code(locator_strategy, locator, timeout)
            elif action in ['type', 'enter_text', 'input']:
                function_code += self._generate_type_code(locator_strategy, locator, test_data_param, timeout)
            elif action == 'select':
                function_code += self._generate_select_code(locator_strategy, locator, test_data_param, timeout)
            elif action == 'wait':
                function_code += self._generate_wait_code(locator_strategy, locator, timeout)
            else:
                logger.warning(f"Unknown action type: {action}")
                function_code += f'        # Unknown action: {action}\n'
                function_code += f'        pass\n'
            
            # Add exception handling and function footer
            function_code += f'''        
        # Brief pause for stability
        time.sleep(random.uniform(0.3, 0.7))
        
    except Exception as e:
        print(f"Exception in {function_name}: {{repr(e)}}")
        # Take screenshot for debugging
        try:
            browser.save_screenshot(f"{function_name}_failure.png")
        except:
            pass
        raise

'''
            
            return function_code
            
        except Exception as e:
            logger.error(f"Error generating step function for step {step_no}: {str(e)}")
            return None
    
    def _generate_navigate_code(self, url: str, timeout: int) -> str:
        """Generate navigation code."""
        return f'''        # Navigate to URL
        target_url = "{url}" if "{url}".startswith("http") else TEST_DATA.get("website_url", "{url}")
        browser.get(target_url)
        
        # Wait for page to load
        WebDriverWait(browser, {timeout}).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        # Additional wait for dynamic content
        time.sleep(1.0)
'''
    
    def _generate_click_code(self, locator_strategy: str, locator: str, timeout: int) -> str:
        """Generate click action code."""
        by_locator = self._get_selenium_by_locator(locator_strategy, locator)
        
        return f'''        # Find and click element
        wait = WebDriverWait(browser, {timeout})
        element = wait.until(EC.element_to_be_clickable({by_locator}))
        
        # Scroll element into view
        browser.execute_script("arguments[0].scrollIntoView({{block: 'center'}});", element)
        time.sleep(0.3)
        
        # Click element
        element.click()
'''
    
    def _generate_type_code(self, locator_strategy: str, locator: str, text: str, timeout: int) -> str:
        """Generate type/input action code."""
        by_locator = self._get_selenium_by_locator(locator_strategy, locator)
        
        return f'''        # Find and type into element
        wait = WebDriverWait(browser, {timeout})
        element = wait.until(EC.element_to_be_clickable({by_locator}))
        
        # Clear and type text
        element.clear()
        element.send_keys("{text}")
'''
    
    def _generate_select_code(self, locator_strategy: str, locator: str, value: str, timeout: int) -> str:
        """Generate select dropdown action code."""
        by_locator = self._get_selenium_by_locator(locator_strategy, locator)
        
        return f'''        # Find and select from dropdown
        wait = WebDriverWait(browser, {timeout})
        element = wait.until(EC.element_to_be_clickable({by_locator}))
        
        # Select option
        select = Select(element)
        try:
            select.select_by_visible_text("{value}")
        except NoSuchElementException:
            try:
                select.select_by_value("{value}")
            except NoSuchElementException:
                select.select_by_index(int("{value}"))
'''
    
    def _generate_wait_code(self, locator_strategy: str, locator: str, timeout: int) -> str:
        """Generate wait action code."""
        if locator_strategy and locator:
            by_locator = self._get_selenium_by_locator(locator_strategy, locator)
            return f'''        # Wait for element to appear
        wait = WebDriverWait(browser, {timeout})
        wait.until(EC.presence_of_element_located({by_locator}))
'''
        else:
            return f'''        # Simple time-based wait
        time.sleep({min(timeout, 5)})
'''
    
    def _get_selenium_by_locator(self, locator_strategy: str, locator: str) -> str:
        """Convert locator strategy to Selenium By locator string."""
        strategy = locator_strategy.lower()
        
        if strategy == 'id':
            return f'(By.ID, "{locator}")'
        elif strategy == 'name':
            return f'(By.NAME, "{locator}")'
        elif strategy in ['css', 'css_selector']:
            return f'(By.CSS_SELECTOR, "{locator}")'
        elif strategy == 'xpath':
            return f'(By.XPATH, "{locator}")'
        elif strategy == 'class':
            return f'(By.CLASS_NAME, "{locator}")'
        elif strategy == 'tag':
            return f'(By.TAG_NAME, "{locator}")'
        elif strategy == 'link_text':
            return f'(By.LINK_TEXT, "{locator}")'
        elif strategy == 'partial_link_text':
            return f'(By.PARTIAL_LINK_TEXT, "{locator}")'
        else:
            logger.warning(f"Unknown locator strategy: {locator_strategy}")
            return f'(By.CSS_SELECTOR, "{locator}")'  # Fallback to CSS
    
    def _generate_script_footer(self) -> str:
        """Generate script footer with browser session preservation instructions."""
        return '''
# =========================================================================
# BROWSER SESSION PRESERVATION
# =========================================================================
# The browser session is maintained after this script execution for use
# with the interactive element selector. The browser state should reflect
# the completion of all steps included in this partial script.
# =========================================================================
'''
    
    def _save_partial_script(self, script_content: str, test_case_id: str, current_step_index: int) -> Optional[str]:
        """
        Save the generated partial script to a temporary file.
        
        Args:
            script_content: Generated script content
            test_case_id: Test case identifier
            current_step_index: Current step index
            
        Returns:
            Path to saved script file, or None if save fails
        """
        try:
            # Create filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"partial_script_{test_case_id}_to_step_{current_step_index}_{timestamp}.py"
            
            # Get GretahAI_ScriptWeaver directory
            scriptweaver_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            script_path = os.path.join(scriptweaver_dir, filename)
            
            # Write script content to file
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            logger.info(f"Saved partial script to: {script_path}")
            return script_path
            
        except Exception as e:
            logger.error(f"Error saving partial script: {str(e)}")
            return None
    
    def _execute_partial_script(self, script_path: str, test_case_id: str) -> Tuple[bool, str, Optional[str]]:
        """
        Execute the partial script using pytest infrastructure.
        
        Args:
            script_path: Path to the partial script file
            test_case_id: Test case identifier
            
        Returns:
            Tuple of (success: bool, message: str, browser_session_info: Optional[str])
        """
        try:
            logger.info(f"Executing partial script: {script_path}")
            
            # Get GretahAI_ScriptWeaver directory for proper conftest.py access
            scriptweaver_dir = os.path.dirname(script_path)
            
            # Set environment variables for the test run
            env = os.environ.copy()
            env["HEADLESS"] = "0"  # Always run in visible mode for state building
            env["PYTEST_QUIET_MODE"] = "1"  # Quiet mode for cleaner output
            
            # Build pytest command
            pytest_command = [
                "pytest",
                script_path,
                "-v",  # Verbose output for debugging
                "--tb=short",  # Short traceback format
                "--capture=no",  # Don't capture output
                f"--rootdir={scriptweaver_dir}",  # Set rootdir for conftest.py
                "--disable-warnings",  # Reduce noise
            ]
            
            # Execute pytest command
            start_time = time.time()
            result = subprocess.run(
                pytest_command,
                cwd=scriptweaver_dir,
                env=env,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            execution_time = time.time() - start_time
            
            # Store execution results
            self.execution_results = {
                "returncode": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "execution_time": execution_time,
                "script_path": script_path
            }
            
            if result.returncode == 0:
                success_message = f"Partial script executed successfully in {execution_time:.2f}s"
                logger.info(success_message)
                
                # Browser session info (placeholder for future browser session management)
                browser_info = f"Browser state prepared by {os.path.basename(script_path)}"
                
                return True, success_message, browser_info
            else:
                error_message = f"Partial script execution failed (exit code: {result.returncode})"
                logger.error(error_message)
                logger.error(f"STDOUT: {result.stdout}")
                logger.error(f"STDERR: {result.stderr}")
                
                return False, error_message, None
                
        except subprocess.TimeoutExpired:
            error_message = "Partial script execution timed out"
            logger.error(error_message)
            return False, error_message, None
        except Exception as e:
            error_message = f"Error executing partial script: {str(e)}"
            logger.error(error_message, exc_info=True)
            return False, error_message, None
    
    def cleanup(self):
        """Clean up temporary files and resources."""
        try:
            if self.partial_script_path and os.path.exists(self.partial_script_path):
                os.remove(self.partial_script_path)
                logger.info(f"Cleaned up partial script: {self.partial_script_path}")
        except Exception as e:
            logger.warning(f"Error during cleanup: {str(e)}")
